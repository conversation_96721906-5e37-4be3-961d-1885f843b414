#!/usr/bin/env python3
"""
Java代码库切片演示脚本
输入：Java代码库路径
输出：结构化的代码切片
"""

import asyncio
import json
import sys
from pathlib import Path
from typing import List, Dict, Any
import argparse

# 添加项目路径以便导入模块
sys.path.insert(0, str(Path(__file__).parent.parent))

from vector_search.parsers.tree_sitter_parser import TreeSitterParser
from vector_search.models import CodeBlock
from vector_search.constants import SUPPORTED_EXTENSIONS


class JavaCodeSlicer:
    """Java代码切片器"""
    
    def __init__(self):
        self.parser = TreeSitterParser()
        self.total_files = 0
        self.processed_files = 0
        self.total_blocks = 0
        
    def find_java_files(self, directory: str) -> List[str]:
        """查找目录中的所有Java文件"""
        java_files = []
        directory_path = Path(directory)
        
        if not directory_path.exists():
            raise ValueError(f"目录不存在: {directory}")
        
        # 递归查找.java文件
        for java_file in directory_path.rglob("*.java"):
            if java_file.is_file():
                # 跳过一些常见的构建目录
                if any(part in str(java_file) for part in ['target', 'build', '.git', 'node_modules']):
                    continue
                java_files.append(str(java_file))
        
        return java_files
    
    async def slice_java_file(self, file_path: str) -> List[CodeBlock]:
        """切片单个Java文件"""
        try:
            print(f"正在处理: {file_path}")
            
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 使用解析器进行切片
            blocks = await self.parser.parse_file(file_path, content=content)
            
            self.processed_files += 1
            self.total_blocks += len(blocks)
            
            print(f"  ✓ 提取了 {len(blocks)} 个代码块")
            return blocks
            
        except Exception as e:
            print(f"  ✗ 处理失败: {e}")
            return []
    
    async def slice_java_repository(self, repo_path: str) -> Dict[str, Any]:
        """切片整个Java代码库"""
        print(f"开始切片Java代码库: {repo_path}")
        print("=" * 60)
        
        # 查找所有Java文件
        java_files = self.find_java_files(repo_path)
        self.total_files = len(java_files)
        
        if not java_files:
            print("未找到Java文件")
            return {"files": [], "summary": {"total_files": 0, "processed_files": 0, "total_blocks": 0}}
        
        print(f"找到 {len(java_files)} 个Java文件")
        print("-" * 60)
        
        # 处理每个文件
        all_slices = {}
        for file_path in java_files:
            blocks = await self.slice_java_file(file_path)
            if blocks:
                all_slices[file_path] = blocks
        
        # 生成摘要
        summary = {
            "total_files": self.total_files,
            "processed_files": self.processed_files,
            "total_blocks": self.total_blocks,
            "success_rate": f"{(self.processed_files/self.total_files)*100:.1f}%" if self.total_files > 0 else "0%"
        }
        
        return {
            "repository_path": repo_path,
            "files": all_slices,
            "summary": summary
        }
    
    def format_code_block(self, block: CodeBlock) -> Dict[str, Any]:
        """格式化代码块为可读的字典"""
        return {
            "file_path": block.file_path,
            "identifier": block.identifier,
            "type": block.type,
            "start_line": block.start_line,
            "end_line": block.end_line,
            "line_count": block.end_line - block.start_line + 1,
            "content_preview": block.content[:200] + "..." if len(block.content) > 200 else block.content,
            "content_length": len(block.content),
            "segment_hash": block.segment_hash[:8] + "...",  # 只显示前8位
        }
    
    def print_slice_summary(self, slices: Dict[str, Any]):
        """打印切片摘要"""
        print("\n" + "=" * 60)
        print("切片摘要")
        print("=" * 60)
        
        summary = slices["summary"]
        print(f"代码库路径: {slices['repository_path']}")
        print(f"总文件数: {summary['total_files']}")
        print(f"处理成功: {summary['processed_files']}")
        print(f"成功率: {summary['success_rate']}")
        print(f"总代码块: {summary['total_blocks']}")
        
        if summary['total_blocks'] > 0:
            print(f"平均每文件代码块数: {summary['total_blocks']/summary['processed_files']:.1f}")
        
        print("\n" + "-" * 60)
        print("代码块类型统计:")
        
        # 统计代码块类型
        type_counts = {}
        for file_path, blocks in slices["files"].items():
            for block in blocks:
                block_type = block.type
                type_counts[block_type] = type_counts.get(block_type, 0) + 1
        
        for block_type, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"  {block_type}: {count}")
    
    def print_detailed_slices(self, slices: Dict[str, Any], max_files: int = 3):
        """打印详细的切片信息"""
        print("\n" + "=" * 60)
        print("详细切片信息 (显示前{}个文件)".format(max_files))
        print("=" * 60)
        
        file_count = 0
        for file_path, blocks in slices["files"].items():
            if file_count >= max_files:
                break
            
            print(f"\n文件: {file_path}")
            print(f"代码块数量: {len(blocks)}")
            print("-" * 40)
            
            for i, block in enumerate(blocks, 1):
                formatted_block = self.format_code_block(block)
                print(f"  {i}. [{formatted_block['type']}] {formatted_block['identifier'] or '(无标识符)'}")
                print(f"     行数: {formatted_block['start_line']}-{formatted_block['end_line']} ({formatted_block['line_count']} 行)")
                print(f"     长度: {formatted_block['content_length']} 字符")
                print(f"     哈希: {formatted_block['segment_hash']}")
                
                # 显示代码预览
                preview_lines = formatted_block['content_preview'].split('\n')[:3]
                for line in preview_lines:
                    if line.strip():
                        print(f"     | {line}")
                print()
            
            file_count += 1
        
        remaining_files = len(slices["files"]) - max_files
        if remaining_files > 0:
            print(f"\n... 还有 {remaining_files} 个文件未显示")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Java代码库切片工具")
    parser.add_argument("repository", help="Java代码库路径")
    parser.add_argument("--output", "-o", help="输出JSON文件路径")
    parser.add_argument("--detailed", "-d", action="store_true", help="显示详细切片信息")
    parser.add_argument("--max-files", type=int, default=3, help="详细显示的最大文件数")
    
    args = parser.parse_args()
    
    # 检查输入路径
    if not Path(args.repository).exists():
        print(f"错误: 路径不存在 {args.repository}")
        sys.exit(1)
    
    # 创建切片器
    slicer = JavaCodeSlicer()
    
    try:
        # 执行切片
        slices = await slicer.slice_java_repository(args.repository)
        
        # 打印摘要
        slicer.print_slice_summary(slices)
        
        # 打印详细信息
        if args.detailed:
            slicer.print_detailed_slices(slices, args.max_files)
        
        # 保存到文件
        if args.output:
            output_data = {
                "repository_path": slices["repository_path"],
                "summary": slices["summary"],
                "files": {}
            }
            
            # 转换CodeBlock对象为字典
            for file_path, blocks in slices["files"].items():
                output_data["files"][file_path] = [
                    slicer.format_code_block(block) for block in blocks
                ]
            
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            
            print(f"\n✓ 切片结果已保存到: {args.output}")
        
        print("\n✓ 切片完成!")
        
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
